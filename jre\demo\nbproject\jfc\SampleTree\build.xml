<?xml version="1.0" encoding="UTF-8"?>

<!--
 Copyright (c) 2006, Oracle and/or its affiliates. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:

   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

   - Neither the name of Oracle nor the names of its
     contributors may be used to endorse or promote products derived
     from this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
 IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<project name="SampleTree" basedir="." default="jar">

    <import file="nbproject/jdk.xml"/>
    
    <target name="-prop-init">
        <property file="user.build.properties"/>
        <property file="build.properties"/>
    </target>

    <target name="-init" depends="-prop-init,-jdk-init"/>

    <target name="compile" depends="-init" description="Compile main sources.">
        <mkdir dir="${classes.dir}"/>
        <javac srcdir="${src.dir}" destdir="${classes.dir}" debug="${debug}" deprecation="${deprecation}">
            <classpath path="${cp}"/>
        </javac>
        <copy todir="${classes.dir}">
            <fileset dir="${src.dir}"/>
        </copy>
    </target>

    <target name="jar" depends="compile" description="Build JAR file for main sources.">
        <jar jarfile="${jar}" compress="true">
            <manifest>
                <attribute name="Main-Class" value="${main.class}"/>
            </manifest>
            <fileset dir="${classes.dir}"/>
        </jar>
    </target>

    <target name="run" depends="compile" description="Run application.">
        <fail unless="main.class">Must set property 'main.class' (e.g. in build.properties)</fail>
        <java classname="${main.class}" fork="true" failonerror="true">
            <classpath path="${run.cp}"/>
        </java>
    </target>

    <target name="javadoc" depends="-init" description="Build Javadoc.">
        <mkdir dir="${javadoc.dir}"/>
        <javadoc destdir="${javadoc.dir}">
            <classpath path="${cp}"/>
            <sourcepath>
                <pathelement location="${src.dir}"/>
            </sourcepath>
            <fileset dir="${src.dir}"/>
        </javadoc>
    </target>

    <target name="clean" depends="-init" description="Clean build products.">
        <delete dir="${build.dir}"/>
        <delete file="${jar}"/>
    </target>

    <target name="profile">
        <ant antfile="nbproject/netbeans-targets.xml" target="profile"/>
    </target>

</project>
