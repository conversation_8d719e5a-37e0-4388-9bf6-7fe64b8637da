<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta content="text/html; charset=UTF-8" http-equiv="content-type">
  <title>README - CodePointIM</title>
</head>
<body>
<h1>Code Point Input Method</h1>
<p>
Code Point Input Methodは、コードポイントまたはコード単位を使ってUnicode文字を入力するシンプルなインプットメソッドです。
<p>
このインプットメソッドでは、十六進数（0～9、a～f、A～F）を使った３つの入力形式をサポートします。
<br>
<ul>
  <li>"\uxxxx"：Javaプログラミング言語の標準Unicodeエスケープ表記です。この表記法では、U+0000からU+FFFEまでのコードポイントを入力できます。不正なコードポイントであるU+FFFFは入力できません。</li>
  <li>"\Uxxxxxx"：このインプットメソッド限定の拡張Unicodeエスケープ表記です。この表記法では、すべての有効なUnicodeコードポイントを直接入力できます。大文字の'U'は、後に６桁の十六進数が続くことを示しています。"xxxxxx"に指定できる値は000000から10FFFFまでで、このうち不正なコードポイントであるU+FFFF(00FFFF)は入力できません。</li>
  <li>"\uxxxx\uyyyy"：連続する２つの標準Unicodeエスケープ表記です。２つ合わせてU+10000からU+10FFFFまでの範囲のコードポイントを持つ補助文字(supplementary character)を表します。"xxxx"はD800以上DBFF以下（上位サロゲート値）、"yyyy"はDC00以上DFFF以下（下位サロゲート値）の範囲でなければなりません。</li>
</ul>
このインプットメソッドは、通常は入力された文字をそのままアプリケーション・プログラムに渡します（パススルー・モード）。しかし、ユーザが文字'\'をタイプするとコンポジション・モードになり、この状態でユーザは上記の入力形式のいずれかを使って目的のコードポイントを入力し、Spaceキーを押して入力されたコードポイントに相当するUnicode文字に変換・確定することができます。確定によってコンポジション・モードは終了し、その後再び'\'がタイプされるまでインプットメソッドはパススルー・モードで動作します。 
<p>
コンポジション・モードでは、ユーザはBackspace、Deleteおよび左右の矢印キーを使って変換テキストを編集することができます。"\u"および"\U"は、変換テキスト中で後続する文字がない場合のみ削除可能です。先頭の"\u"または"\U"を削除すると、インプットメソッドはパススルー・モードに戻ります。 
<p>
最初の'\'のタイプによってコンポジション・モードになるため、'\'を確定済みテキストにするためには合計二度タイプする必要があります。'\'の次にタイプされた文字が'u'、'U'、'\'のいずれでもない場合、'\'とその次にタイプされた文字の両方が確定され、インプットメソッドはパススルー・モードに戻ります。また、コンポジション・モード中にTabまたはEnterキーを押すと、インプットメソッドは現在の変換テキストをそのまま確定してパススルー・モードに戻ります。 
<p>
インプットメソッドはJava Runtime Environmentの拡張機能であり、アプリケーション・プログラムとして動かすことはできません。インプットメソッドを使用する場合、はじめにインプットメソッドをJREにインストールし、次にインプットメソッドをサポートするアプリケーション・プログラム（例えばJFCデモのNotepadやStylepad）を起動してインプットメソッドを選択してください。これらの手順については"<a href="http://javadesktop.org/articles/InputMethod/index.html">Using Input Methods on the Java Platform</a>"をご参照ください。また、補助文字サポートの概要については"<a href="http://java.sun.com/developer/technicalArticles/Intl/Supplementary/index_ja.html">Javaプラットフォームにおける補助文字のサポート</a>"をご参照ください。 
<p>
このインプットメソッドを使用するためには、JRE 5.0以降が必要です。
</body>
</html>
