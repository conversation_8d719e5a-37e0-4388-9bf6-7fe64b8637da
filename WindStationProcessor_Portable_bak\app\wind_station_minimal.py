#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind Station Processor - 最小化交互版本
专门用于打包成exe，减少依赖和启动时间
"""

import os
import sys
import time

def print_banner():
    """显示程序标题"""
    print("=" * 60)
    print("    新疆风能站CF卡文件10分钟统计值计算程序")
    print("    Wind Station Processor v1.0")
    print("=" * 60)
    print()

def get_file_input(prompt, required=True):
    """获取文件路径输入"""
    while True:
        print(f"[FILE] {prompt}")
        if not required:
            print("   (直接按回车跳过，将自动生成文件名)")
        
        file_path = input("请输入路径: ").strip()
        
        # 移除引号
        if file_path.startswith('"') and file_path.endswith('"'):
            file_path = file_path[1:-1]
        elif file_path.startswith("'") and file_path.endswith("'"):
            file_path = file_path[1:-1]
        
        # 如果不是必需的且为空，返回None
        if not required and not file_path:
            return None
        
        # 检查文件是否存在
        if file_path and os.path.exists(file_path):
            print(f"[OK] 文件路径有效: {file_path}")
            return file_path
        elif file_path:
            print(f"[ERROR] 错误: 文件不存在: {file_path}")
            print("   请检查路径是否正确")
        else:
            print("[ERROR] 错误: 请输入文件路径")
        
        print()

def check_file_exists(file_path):
    """检查文件是否存在，并打印友好信息"""
    if os.path.exists(file_path):
        print(f"[OK] 文件路径有效: {file_path}")
        return True
    else:
        print(f"[ERROR] 错误: 文件不存在: {file_path}")
        print("   请检查路径是否正确")
        return False

def main():
    """主函数"""
    print("=== 新疆风能站CF卡文件10分钟统计值计算程序 - 精简版 ===")
    print("针对快速处理进行优化，适合小文件处理")
    print()

    # 默认文件名
    data_file = "202411.csv"
    status_file = "202411状态信息.csv"

    # 检查文件是否存在
    if not check_file_exists(data_file):
        print(f"[ERROR] 错误：未找到数据文件 {data_file}")
        print("请确保CSV文件存在于当前目录中")
        return

    # 获取文件大小
    try:
        data_size = os.path.getsize(data_file) / (1024 * 1024)  # MB
        print(f"[OK] 数据文件: {data_file} ({data_size:.1f} MB)")

        if os.path.exists(status_file):
            status_size = os.path.getsize(status_file) / 1024  # KB
            print(f"[OK] 状态文件: {status_file} ({status_size:.1f} KB)")
        else:
            print(f"[WARN] 状态文件不存在: {status_file}")
            print("将使用默认电压值")
    except Exception as e:
        print(f"[WARN] 无法获取文件大小: {e}")

    print()

    try:
        print("\n[INFO] 正在启动数据处理...")
        
        # 导入处理模块
        from wind_station_processor_ultra_optimized import process_wind_station_data
        print("[OK] 模块导入成功")
        
        # 调用处理函数
        output_file = process_wind_station_data(data_file, status_file)
        
        print(f"[SUCCESS] 数据处理完成！")
        print(f"输出文件: {output_file}")
        
    except ImportError as e:
        print(f"[ERROR] 模块导入失败: {e}")
        print("请确保所有依赖模块都已正确安装")
    except Exception as e:
        print(f"[ERROR] 处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n" + "=" * 60)
        print("感谢使用 Wind Station Processor！")
        print("=" * 60)
        input("\n按回车键退出...")

if __name__ == '__main__':
    main()
