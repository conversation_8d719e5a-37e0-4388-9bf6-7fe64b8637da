@echo off
REM Simple Portable Wind Station Processor call script

setlocal

REM Get script directory
set SCRIPT_DIR=%~dp0

REM Set portable Python path
set PORTABLE_PYTHON=%SCRIPT_DIR%python\python.exe

REM Check if portable Python exists
if not exist "%PORTABLE_PYTHON%" (
    echo ERROR: Portable Python not found
    exit /b 1
)

REM Change to app directory and run
cd /d "%SCRIPT_DIR%app"
"%PORTABLE_PYTHON%" "..\run_cli.py" %*

REM Return exit code
exit /b %ERRORLEVEL%
