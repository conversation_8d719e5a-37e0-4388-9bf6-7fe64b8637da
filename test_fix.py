#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
模拟Java程序调用Python处理程序的场景
"""

import sys
import json
import os
import subprocess

def test_python_encoding():
    """测试Python编码处理"""
    try:
        # 模拟原来会出错的Unicode字符输出
        test_messages = [
            "[OK] 测试通过",
            "[ERROR] 测试错误", 
            "[WARN] 测试警告",
            "[INFO] 测试信息",
            "[SUCCESS] 测试成功"
        ]
        
        for msg in test_messages:
            print(msg)
        
        # 输出JSON格式结果（模拟实际程序的输出）
        result = {
            "success": True,
            "message": "编码测试成功",
            "output_file": "test_output.csv"
        }
        
        print(json.dumps(result, ensure_ascii=False))
        return True
        
    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e),
            "output_file": None
        }
        print(json.dumps(error_result, ensure_ascii=False))
        return False

def main():
    """主函数"""
    print("=== 编码修复效果测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"当前编码: {sys.getdefaultencoding()}")
    print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '未设置')}")
    print(f"PYTHONUTF8: {os.environ.get('PYTHONUTF8', '未设置')}")
    print()
    
    success = test_python_encoding()
    
    if success:
        print("\n=== 测试结果 ===")
        print("[SUCCESS] 编码修复成功！")
        print("- 所有字符都能正确输出")
        print("- JSON格式输出正常")
        print("- 不再出现GBK编码错误")
        sys.exit(0)
    else:
        print("\n=== 测试结果 ===")
        print("[ERROR] 编码修复失败！")
        print("- 仍然存在编码问题")
        print("- 需要进一步检查环境配置")
        sys.exit(1)

if __name__ == '__main__':
    main()
