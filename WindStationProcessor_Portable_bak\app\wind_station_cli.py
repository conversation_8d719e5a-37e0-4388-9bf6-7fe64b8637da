#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind Station Processor - 命令行包装器
用于exe打包和Java调用
"""

import sys
import os
import argparse
import json
from wind_station_processor_ultra_optimized import process_wind_station_data

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='新疆风能站CF卡文件10分钟统计值计算程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s data.csv status.csv                    # 自动生成输出文件名
  %(prog)s data.csv status.csv -o output.csv      # 指定输出文件名
  %(prog)s 202411.csv "202411状态信息.csv"        # 处理202411月数据
  %(prog)s data.csv status.csv --json             # JSON格式输出
        """
    )

    parser.add_argument('data_file',
                       help='风速风向数据CSV文件路径')
    parser.add_argument('status_file',
                       help='状态信息CSV文件路径')
    parser.add_argument('-o', '--output',
                       dest='output_file',
                       help='输出文件路径（可选，不指定则自动生成）')
    parser.add_argument('--json',
                       action='store_true',
                       help='以JSON格式输出结果（便于Java解析）')
    parser.add_argument('--version',
                       action='version',
                       version='Wind Station Processor v1.0')

    # 解析命令行参数
    args = parser.parse_args()

    try:
        # 验证输入文件
        if not os.path.exists(args.data_file):
            error_msg = f"数据文件不存在: {args.data_file}"
            if args.json:
                result = {"success": False, "error": error_msg, "output_file": None}
                print(json.dumps(result, ensure_ascii=False))
            else:
                print(f"错误: {error_msg}", file=sys.stderr)
            sys.exit(1)

        if not os.path.exists(args.status_file):
            warning_msg = f"状态文件不存在: {args.status_file}"
            if not args.json:
                print(f"警告: {warning_msg}", file=sys.stderr)
                print("将使用默认电压值", file=sys.stderr)

        # 调用处理函数
        output_file = process_wind_station_data(
            data_file=args.data_file,
            status_file=args.status_file,
            output_file=args.output_file
        )

        # 输出结果
        if args.json:
            # JSON格式输出（便于Java解析）
            result = {
                "success": True,
                "error": None,
                "output_file": output_file,
                "data_file": args.data_file,
                "status_file": args.status_file
            }
            print(json.dumps(result, ensure_ascii=False))
        else:
            # 简单格式输出
            print(f"SUCCESS:{output_file}")

        sys.exit(0)

    except FileNotFoundError as e:
        error_msg = str(e)
        if args.json:
            result = {"success": False, "error": error_msg, "output_file": None}
            print(json.dumps(result, ensure_ascii=False))
        else:
            print(f"错误: {error_msg}", file=sys.stderr)
        sys.exit(1)

    except Exception as e:
        error_msg = f"处理失败: {str(e)}"
        if args.json:
            result = {"success": False, "error": error_msg, "output_file": None}
            print(json.dumps(result, ensure_ascii=False))
        else:
            print(error_msg, file=sys.stderr)
        sys.exit(2)

if __name__ == '__main__':
    main()
