main.dir=${basedir}/../../../jfc/FileChooserDemo

src.dir=${main.dir}/src

build.dir=build
classes.dir=${build.dir}/classes
jar=${main.dir}/FileChooserDemo.jar
javadoc.dir=${build.dir}/javadoc

build.sysclasspath=ignore
# E.g.: cp=lib/x.jar:lib/y.jar
cp=
extra.run.cp=

main.class=FileChooserDemo

run.cp=${cp}:${classes.dir}:${extra.run.cp}

debug=true
deprecation=false

nbjdk.home=${basedir}/../../../..
