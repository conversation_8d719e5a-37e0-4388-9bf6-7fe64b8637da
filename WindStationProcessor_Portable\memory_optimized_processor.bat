@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ========================================
echo 内存优化版风能站数据处理器
echo 专门处理大文件和内存不足的情况
echo ========================================

REM 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "PYTHON_EXE=%SCRIPT_DIR%python\python.exe"
set "APP_DIR=%SCRIPT_DIR%app"
set "MEMORY_SCRIPT=%APP_DIR%\memory_optimized_processor.py"

REM 检查Python环境
if not exist "%PYTHON_EXE%" (
    echo 便携式Python不存在: %PYTHON_EXE%
    echo 请确保python目录存在并包含python.exe
    pause
    exit /b 1
)

REM 检查脚本文件
if not exist "%MEMORY_SCRIPT%" (
    echo 内存优化脚本不存在: %MEMORY_SCRIPT%
    echo 请确保app目录包含memory_optimized_processor.py
    pause
    exit /b 1
)

REM 检查参数
if "%~1"=="" (
    echo 用法: %0 ^<数据文件^> ^<状态文件^> [输出文件]
    echo.
    echo 示例:
    echo   %0 "data.csv" "status.csv"
    echo   %0 "data.csv" "status.csv" "output.csv"
    pause
    exit /b 1
)

if "%~2"=="" (
    echo 缺少状态文件参数
    echo 用法: %0 ^<数据文件^> ^<状态文件^> [输出文件]
    pause
    exit /b 1
)

REM 设置参数
set "DATA_FILE=%~1"
set "STATUS_FILE=%~2"
set "OUTPUT_FILE=%~3"

REM 检查输入文件
if not exist "%DATA_FILE%" (
    echo 数据文件不存在: %DATA_FILE%
    pause
    exit /b 1
)

if not exist "%STATUS_FILE%" (
    echo 状态文件不存在: %STATUS_FILE%
    pause
    exit /b 1
)

echo 数据文件: %DATA_FILE%
echo 状态文件: %STATUS_FILE%
if not "%OUTPUT_FILE%"=="" (
    echo 输出文件: %OUTPUT_FILE%
)

echo.
echo 启动内存优化处理...

REM 设置工作目录
cd /d "%APP_DIR%"

REM 执行Python脚本
if not "%OUTPUT_FILE%"=="" (
    "%PYTHON_EXE%" "%MEMORY_SCRIPT%" "%DATA_FILE%" "%STATUS_FILE%" "%OUTPUT_FILE%"
) else (
    "%PYTHON_EXE%" "%MEMORY_SCRIPT%" "%DATA_FILE%" "%STATUS_FILE%"
)

set "EXIT_CODE=%ERRORLEVEL%"

if %EXIT_CODE% equ 0 (
    echo.
    echo 处理完成！
) else (
    echo.
    echo 处理失败，退出码: %EXIT_CODE%
)

echo.
pause
exit /b %EXIT_CODE%
