#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码测试脚本
用于验证Python环境是否能正确处理Unicode字符
"""

import sys
import json
import os

def test_encoding():
    """测试编码功能"""
    try:
        # 测试基本的ASCII字符
        print("[OK] ASCII字符测试通过")
        
        # 测试中文字符
        print("[OK] 中文字符测试通过")
        
        # 测试JSON输出（这是Java程序期望的格式）
        result = {
            "success": True,
            "message": "编码测试成功",
            "details": "所有字符都能正确处理"
        }
        
        # 输出JSON结果
        print(json.dumps(result, ensure_ascii=False))
        
        return True
        
    except Exception as e:
        # 如果出现编码错误，输出错误信息
        error_result = {
            "success": False,
            "error": str(e),
            "message": "编码测试失败"
        }
        print(json.dumps(error_result, ensure_ascii=False))
        return False

def main():
    """主函数"""
    print("=== Python编码测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    
    # 检查环境变量
    print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '未设置')}")
    print(f"PYTHONUTF8: {os.environ.get('PYTHONUTF8', '未设置')}")
    print()
    
    # 执行编码测试
    success = test_encoding()
    
    if success:
        print("\n[SUCCESS] 编码测试完成，所有功能正常")
        sys.exit(0)
    else:
        print("\n[ERROR] 编码测试失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
