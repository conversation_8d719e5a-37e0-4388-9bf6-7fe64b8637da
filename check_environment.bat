@echo off
echo ========================================
echo WindCodeFx Environment Check
echo ========================================
echo.

setlocal

set curpath=%~dp0
pushd %curpath%

echo Current directory: %cd%
echo.

rem Check if JRE exists
echo [1] Checking JRE installation...
set JAVA_HOME=%cd%\jre
if exist %JAVA_HOME% (
    echo [OK] JRE found in current directory: %JAVA_HOME%
) else (
    echo [ERROR] JRE not found in current directory
    echo   Trying parent directory...
    chdir ..\
    set JAVA_HOME=%cd%\jre
    if exist %JAVA_HOME% (
        echo [OK] JRE found in parent directory: %JAVA_HOME%
    ) else (
        echo [ERROR] JRE not found in parent directory either
        echo   Please ensure jre folder exists
    )
)

echo.

rem Check if java.exe exists
echo [2] Checking java.exe...
if exist "%JAVA_HOME%\bin\java.exe" (
    echo [OK] java.exe found: %JAVA_HOME%\bin\java.exe
) else (
    echo [ERROR] java.exe not found in %JAVA_HOME%\bin\
)

echo.

rem Check if windCodeFx-1.0-SNAPSHOT.exe exists
echo [3] Checking windCodeFx-1.0-SNAPSHOT.exe...
if exist "%JAVA_HOME%\bin\windCodeFx-1.0-SNAPSHOT.exe" (
    echo [OK] windCodeFx-1.0-SNAPSHOT.exe found
    echo   Testing if it's accessible...
    "%JAVA_HOME%\bin\windCodeFx-1.0-SNAPSHOT.exe" -version >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo [OK] windCodeFx-1.0-SNAPSHOT.exe is accessible
    ) else (
        echo [ERROR] windCodeFx-1.0-SNAPSHOT.exe exists but is not accessible
        echo   This might be due to file permissions or file corruption
    )
) else (
    echo [ERROR] windCodeFx-1.0-SNAPSHOT.exe not found
    echo   This file should be created by the start.bat script
)

echo.

rem Check if JAR file exists
echo [4] Checking JAR file...
if exist "windCodeFx-1.0-SNAPSHOT.jar" (
    echo [OK] windCodeFx-1.0-SNAPSHOT.jar found
) else (
    echo [ERROR] windCodeFx-1.0-SNAPSHOT.jar not found
)

echo.

rem Check file permissions
echo [5] Checking file permissions...
echo   Testing write access to JRE bin directory...
echo test > "%JAVA_HOME%\bin\test_write.tmp" 2>nul
if exist "%JAVA_HOME%\bin\test_write.tmp" (
    echo [OK] Write access to JRE bin directory: OK
    del "%JAVA_HOME%\bin\test_write.tmp" 2>nul
) else (
    echo [ERROR] No write access to JRE bin directory
    echo   This might be due to insufficient permissions
)

echo.

rem Check if any windCodeFx processes are running
echo [6] Checking for running processes...
tasklist /FI "IMAGENAME eq windCodeFx-1.0-SNAPSHOT.exe" 2>NUL | find /I /N "windCodeFx-1.0-SNAPSHOT.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [WARN] Found running windCodeFx-1.0-SNAPSHOT.exe processes
    echo   This might prevent file creation
) else (
    echo [OK] No running windCodeFx-1.0-SNAPSHOT.exe processes found
)

echo.
echo ========================================
echo Environment Check Complete
echo ========================================
echo.
echo If you see any [ERROR] marks above, those are the issues that need to be resolved.
echo.
echo Recommendations:
echo 1. If JRE is missing, ensure the jre folder is present
echo 2. If write access is denied, run as administrator
echo 3. If windCodeFx-1.0-SNAPSHOT.exe is corrupted, delete it and restart
echo 4. If processes are running, close them first
echo.
echo Try running start_simple.bat if start.bat fails.
echo.

endlocal
pause 