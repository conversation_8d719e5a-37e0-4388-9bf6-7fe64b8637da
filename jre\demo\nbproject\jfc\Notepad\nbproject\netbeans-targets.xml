<?xml version="1.0" encoding="UTF-8"?>

<!--
 Copyright (c) 2006, Oracle and/or its affiliates. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:

   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

   - Neither the name of Oracle nor the names of its
     contributors may be used to endorse or promote products derived
     from this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
 IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<project basedir=".." name="Notepad/NB">

    <import file="../build.xml"/>
    
    <target name="debug" depends="compile,-jdk-presetdef-nbjpdastart">
        <nbjpdastart addressproperty="jpda.address" name="Notepad" transport="dt_socket">
            <classpath path="${run.cp}"/>
        </nbjpdastart>
        <java classname="${main.class}" failonerror="true" fork="true">
            <classpath path="${run.cp}"/>
            <jvmarg value="-Xdebug"/>
            <jvmarg value="-Xnoagent"/>
            <jvmarg value="-Djava.compiler=none"/>
            <jvmarg value="-Xrunjdwp:transport=dt_socket,address=${jpda.address}"/>
        </java>
    </target>
    
    <target name="debug-fix" depends="-init">
        <javac srcdir="${src.dir}" destdir="${classes.dir}" debug="true" deprecation="${deprecation}">
            <classpath path="${cp}"/>
            <include name="${class}.java"/>
        </javac>
        <nbjpdareload>
            <fileset dir="${classes.dir}">
                <include name="${class}.class"/>
            </fileset>
        </nbjpdareload>
    </target>
    
    <target name="show-javadoc" depends="javadoc">
        <nbbrowse file="${javadoc.dir}/index.html"/>
    </target>
    
    <target name="profile" depends="compile">
        <nbprofiledirect>
            <classpath path="${run.cp}"/>
        </nbprofiledirect>
        <property environment="env"/>
        <java classname="${main.class}" fork="true" failonerror="true" dir="${profiler.session.working.dir}" jvm="${profiler.info.jvm}">
            <classpath path="${run.cp}"/>
            <jvmarg value="${profiler.info.jvmargs.agent}"/>
            <jvmarg line="${profiler.info.jvmargs}"/>
            <env key="LD_LIBRARY_PATH" path="${profiler.info.agentpath}:${env.LD_LIBRARY_PATH}"/>
            <env key="Path" path="${profiler.info.agentpath}:${env.Path}"/>
        </java>
    </target>
    
</project>
