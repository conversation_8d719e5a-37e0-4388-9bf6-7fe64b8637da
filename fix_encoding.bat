@echo off
REM 修复编码问题的脚本
REM 设置Python环境变量以确保正确的编码处理

echo ========================================
echo 编码问题修复脚本
echo ========================================
echo.

echo [INFO] 正在设置Python编码环境变量...

REM 设置Python使用UTF-8编码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM 设置控制台代码页为UTF-8
chcp 65001 >nul 2>&1

echo [OK] Python编码环境变量已设置
echo   PYTHONIOENCODING=utf-8
echo   PYTHONUTF8=1
echo   控制台代码页=65001 (UTF-8)
echo.

echo [INFO] 验证修复效果...
echo 如果您之前遇到编码错误，现在应该已经解决了。
echo.

echo [INFO] 建议的使用方法：
echo 1. 运行此脚本后，在同一个命令行窗口中启动应用程序
echo 2. 或者将这些环境变量添加到系统环境变量中
echo.

echo ========================================
echo 修复完成
echo ========================================
echo.

pause
