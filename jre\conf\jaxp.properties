################################################################################
#           JAXP Configuration File
#
# jaxp.properties (this file) is the default configuration file for JAXP, the API
# defined in the java.xml module. It is in java.util.Properties format and typically
# located in the {java.home}/conf directory. It may contain key/value pairs for
# specifying the implementation classes of JAXP factories and/or properties
# that have corresponding system properties.
#
# A user-specified configuration file can be set up using the system property
# java.xml.config.file to override any or all of the entries in jaxp.properties.
# The following statement provides myConfigurationFile as a custom configuration
# file:
#     java -Djava.xml.config.file=myConfigurationFile
################################################################################

# ---- JAXP Default Configuration ----
#
# The JAXP default configuration (jaxp.properties) contains entries for the
# Factory Lookup Mechanism and properties with corresponding system properties.
# The values are generally set to the default values of the properties.
#
#
# JAXP Lookup Mechanism:
#
# The JAXP configuration file ranks 2nd to the System Property in the precedent
# order of the JAXP Lookup Mechanism. When the System Property is not specified,
# a JAXP factory reads the configuration file in order to locate an implementation
# class. If found, the class specified will be used as the factory implementation
# class.
#
# The format of an entry is key=value where the key is the fully qualified name
# of the factory and value that of the implementation class. The following entry
# set a DocumentBuilderFactory implementation class:
#
# javax.xml.parsers.DocumentBuilderFactory=com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
#
#
# Java SE and JDK Implementation Specific Properties:
#
# The JAXP configuration file ranks above the default settings in the Property
# Precedence in that its entries will override the default values of the corresponding
# properties.
#
# All properties that have System Properties defined in Java SE or supported
# by the JDK Implementation can be placed in the configuration file to override
# the default property values. The format is:
#     system-property-name=value
#
# For example, the FILES property in CatalogFeatures has an associated system
# property called javax.xml.catalog.files. An entry for the FILES property in the
# configuration file would therefore use javax.xml.catalog.files as the key, that
# is:
#     javax.xml.catalog.files=strict
#
#
# Extension Functions:
#
# This property determines whether XSLT and XPath extension functions are allowed.
# The value type is boolean and the default value is true (allowing
# extension functions). The following entry would override the default value and
# disallow extension functions:
#
# jdk.xml.enableExtensionFunctions=false
#
#
# Overriding the default parser:
#
# This property allows using a third party implementation to override the default
# parser provided by the JDK. The value type is boolean and the default value is
# false, disallowing overriding the default parser. The setting below reflects
# the default property setting:
#
jdk.xml.overrideDefaultParser=false
#
#
# External Access Properties:
#
# The External Access Properties are defined in javax.xml.XMLConstants. Their
# system properties are javax.xml.accessExternalDTD, javax.xml.accessExternalSchema,
# and javax.xml.accessExternalStylesheet. The values are a list of protocols separated
# by comma, plus empty string ("") to represent no protocol allowed and the key
# word "all" for all access. The default is "all", allowing all external resources
# to be fetched. The followings are example of external access settings:
#
# allow local (file) DTDs to be retrieved
# javax.xml.accessExternalDTD=file
#
# allow local (file) and remote (http) external schemas
# javax.xml.accessExternalSchema=file, http
#
# reject any external stylesheets
# javax.xml.accessExternalStylesheet=""
#
# allow all external stylesheets
# javax.xml.accessExternalStylesheet="all"
#
#
# Catalog Properties:
#
# The Catalog API defines four features: FILES, PREFER, DEFER and RESOLVE.
# Except PREFER, all other properties can be placed in the configuration file
# using the system properties defined for them.
#
# FILES: A semicolon-delimited list of URIs to locate the catalog files. The URIs
# must be absolute and have a URL protocol handler for the URI scheme. The following
# is an example of setting up a catalog file:
#
# javax.xml.catalog.files = file:///users/auser/catalog/catalog.xml
#
# DEFER: Indicates that the alternative catalogs including those specified in
# delegate entries or nextCatalog are not read until they are needed. The value
# is a boolean and the default value is true.
#
# javax.xml.catalog.defer=true
#
# RESOLVE: Determines the action if there is no matching entry found after all of
# the specified catalogs are exhausted. The values are key words: strict, continue,
# and ignore. The default is strict. The following setting reflects the default
# setting.
#
# javax.xml.catalog.resolve=strict
#
#
# useCatalog:
# This property instructs XML processors to use XML Catalogs to resolve entity
# references. The value is a boolean and the default value is true.
#
# javax.xml.useCatalog=true
#
#
# Implementation Specific Properties - Limits
#
# Limits have a value type Integer. The values must be positive integers. Zero
# means no limit.
#
# Limits the number of entity expansions. The default value is 64000
# jdk.xml.entityExpansionLimit=64000
#
# Limits the total size of all entities that include general and parameter entities.
# The size is calculated as an aggregation of all entities. The default value is 5x10^7.
# jdk.xml.totalEntitySizeLimit=5E7
#
# Limits the maximum size of any general entities. The default value is 0.
# jdk.xml.maxGeneralEntitySizeLimit=0
#
# Limits the maximum size of any parameter entities, including the result of
# nesting multiple parameter entities. The default value is 10^6.
# jdk.xml.maxParameterEntitySizeLimit=1E6
#
# Limits the total number of nodes in all entity references. The default value is 3x10^6.
# jdk.xml.entityReplacementLimit=3E6
#
# Limits the number of attributes an element can have. The default value is 10000.
# jdk.xml.elementAttributeLimit=10000
#
# Limits the number of content model nodes that may be created when building a
# grammar for a W3C XML Schema that contains maxOccurs attributes with values
# other than "unbounded". The default value is 5000.
# jdk.xml.maxOccurLimit=5000
#
# Limits the maximum element depth. The default value is 0.
# jdk.xml.maxElementDepth=0
#
# Limits the maximum size of XML names, including element name, attribute name
# and namespace prefix and URI. The default value is 1000.
jdk.xml.maxXMLNameLimit=1000
#
#
# XPath Limits
#
# Limits the number of groups an XPath expression can contain. The default value is 10.
jdk.xml.xpathExprGrpLimit=10
#
# Limits the number of operators an XPath expression can contain. The default value is 100.
jdk.xml.xpathExprOpLimit=100
#
# Limits the total number of XPath operators in an XSL Stylesheet. The default value is 10000.
jdk.xml.xpathTotalOpLimit=10000

