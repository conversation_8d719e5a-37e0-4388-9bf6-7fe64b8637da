main.dir=${basedir}/../../../jfc/Font2DTest

src.dir=${main.dir}/src

build.dir=build
classes.dir=${build.dir}/classes
jar=${main.dir}/Font2DTest.jar
javadoc.dir=${build.dir}/javadoc

build.sysclasspath=ignore
# E.g.: cp=lib/x.jar:lib/y.jar
cp=
extra.run.cp=

main.class=Font2DTest

run.cp=${cp}:${classes.dir}:${extra.run.cp}

debug=true
deprecation=false

nbjdk.home=${basedir}/../../../..

applet.html=${main.dir}/Font2DTest.html
