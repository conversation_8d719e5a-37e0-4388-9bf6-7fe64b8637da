@echo off
title Wind Station Processor - 便携式版本
cd /d "%~dp0"

echo ============================================
echo    Wind Station Processor - 便携式版本
echo ============================================
echo.

REM 检查Python是否存在
if not exist "python\python.exe" (
    echo ❌ 错误: 未找到便携式Python
    echo 请先运行 install_dependencies.bat 进行安装
    pause
    exit /b 1
)

REM 设置Python路径
set PYTHONPATH=%~dp0app
set PATH=%~dp0python;%~dp0python\Scripts;%PATH%

REM 启动程序
echo 🚀 启动程序...
python\python.exe app\wind_station_minimal.py

echo.
echo ============================================
echo 程序已结束
echo ============================================
pause
