@echo off
title Wind Station Processor - 依赖安装
color 0A

REM 切换到脚本所在目录
cd /d "%~dp0"

echo ============================================
echo    Wind Station Processor - 依赖安装
echo ============================================
echo.
echo 当前目录: %CD%
echo.

REM 检查目录结构
echo 🔍 检查目录结构...
if exist "python" (
    echo ✅ python目录存在
) else (
    echo ❌ python目录不存在
    echo 正在创建python目录...
    mkdir python
)

if exist "app" (
    echo ✅ app目录存在
) else (
    echo ❌ app目录不存在
)

echo.

REM 检查Python是否存在
echo 🔍 检查便携式Python...
if not exist "python\python.exe" (
    echo ❌ 便携式Python未找到
    echo.
    echo 📋 需要下载便携式Python:
    echo 1. 访问: https://www.python.org/downloads/windows/
    echo 2. 下载: Windows embeddable package (64-bit)
    echo 3. 文件名: python-3.10.11-embed-amd64.zip
    echo 4. 解压到: %CD%\python\
    echo.
    echo 💡 直接下载链接:
    echo https://www.python.org/ftp/python/3.10.11/python-3.10.11-embed-amd64.zip
    echo.
    echo 解压后目录结构应该是:
    echo %CD%\python\python.exe
    echo %CD%\python\python310.dll
    echo %CD%\python\Lib\
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo ✅ 找到便携式Python: python\python.exe
echo.

REM 显示Python版本
echo 🐍 Python版本信息:
python\python.exe --version
echo.

REM 检查pip
echo � 检查pip...
python\python.exe -m pip --version >nul 2>&1
if errorlevel 1 (
    echo �📦 安装pip...
    python\python.exe -m ensurepip --default-pip
    if errorlevel 1 (
        echo ❌ pip安装失败
        echo 可能的原因:
        echo 1. Python embeddable package版本问题
        echo 2. 网络连接问题
        echo 3. 权限问题
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
    echo ✅ pip安装成功
) else (
    echo ✅ pip已存在
)

echo.

REM 检查requirements.txt
echo 🔍 检查依赖文件...
if not exist "app\requirements.txt" (
    echo ⚠️  requirements.txt不存在，创建默认文件...
    echo pandas>=1.3.0> app\requirements.txt
    echo numpy>=1.20.0>> app\requirements.txt
    echo ✅ 创建了默认的requirements.txt
)

echo 📋 依赖包列表:
type app\requirements.txt
echo.

REM 安装依赖
echo 📦 安装依赖包...
echo 这可能需要几分钟时间，请耐心等待...
python\python.exe -m pip install -r app\requirements.txt
if errorlevel 1 (
    echo ⚠️  使用默认源安装失败，尝试使用国内镜像源...
    python\python.exe -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r app\requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 请检查:
        echo 1. 网络连接是否正常
        echo 2. 是否有防火墙阻止
        echo 3. 尝试手动安装: python\python.exe -m pip install pandas numpy
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
)

echo ✅ 依赖包安装成功
echo.

REM 验证安装
echo 🧪 验证安装...
python\python.exe -c "import pandas, numpy; print('✅ pandas和numpy导入成功')"
if errorlevel 1 (
    echo ❌ 依赖包验证失败
    echo 虽然安装成功，但导入失败
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo.
echo ============================================
echo 🎉 安装完成！
echo ============================================
echo.
echo ✅ 便携式Python环境已准备就绪
echo ✅ 所有依赖包已安装并验证
echo.
echo 📋 可用的启动方式:
echo 1. 双击: 启动程序.bat
echo 2. Java调用: PortableJavaClient.java
echo 3. 命令行: portable_processor.bat
echo.
echo 💡 Java调用示例:
echo PortableJavaClient.processWindStationData("data.csv", "status.csv");
echo.
echo 按任意键退出...
pause >nul
