## LibFFI v3.4.4

### LibFFI License
```

libffi - Copyright (c) 1996-2022  Anthony <PERSON>, Red Hat, Inc and others.
See source files for details.

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
``Software''), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED ``AS IS'', WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Copyright (C) 2007-2010  Free Software Foundation, Inc
Copyright (C) 1996-2014  Red Hat, Inc.
Copyright (C) 2009-2012 ARM Ltd.
Copyright (C) 2011 Plausible Labs Cooperative, Inc.
Copyright (C) 2002  Ranjit Mathew
Copyright (C) 2002  Bo Thorsen
Copyright (C) 2002  Roger Sayle
Copyright (C) 2013  The Written Word, Inc.
Copyright (C) 2002-2007  Bo Thorsen <<EMAIL>>

```

### AUTHORS File Information
```

libffi was originally written by Anthony Green <<EMAIL>>.

The developers of the GNU Compiler Collection project have made
innumerable valuable contributions.  See the ChangeLog file for
details.

Some of the ideas behind libffi were inspired by Gianni Mariani's free
gencall library for Silicon Graphics machines.

The closure mechanism was designed and implemented by Kresten Krab
Thorup.

Major processor architecture ports were contributed by the following
developers:

    aarch64             Marcus Shawcroft, James Greenhalgh
    x86                 Anthony Green, Jon Beniston
    x86-64              Bo Thorsen

Jesper Skov and Andrew Haley both did more than their fair share of
stepping through the code and tracking down bugs.

Thanks also to Tom Tromey for bug fixes, documentation and
configuration help.

Thanks to Jim Blandy, who provided some useful feedback on the libffi
interface.

Alex Oliva solved the executable page problem for SElinux.

```
