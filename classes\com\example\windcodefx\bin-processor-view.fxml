<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.example.windcodefx.BinFileProcessorController"
      spacing="20" alignment="TOP_LEFT">
   
   <padding>
      <Insets top="20" right="20" bottom="20" left="20"/>
   </padding>
   
   <!-- 第一部分：源文件选择 -->
   <TitledPane text="源文件选择" expanded="true">
      <VBox spacing="10">
         <HBox spacing="10" alignment="CENTER_LEFT">
            <Label text="源文件夹:" />
            <TextField fx:id="sourcePathField" promptText="请选择格式为年月（如202411）的文件夹" 
                      HBox.hgrow="ALWAYS" editable="false" />
            <Button fx:id="selectSourceButton" text="选择文件夹" onAction="#selectSourceFolder" />
         </HBox>
         <Label text="注意：请选择格式为年月（如202411）的文件夹，系统会自动查找WT_L01和WT_STATUS子文件夹" 
                style="-fx-text-fill: #666666; -fx-font-size: 11px;" />
      </VBox>
   </TitledPane>
   
   <!-- 第二部分：时间选择 -->
   <TitledPane text="时间范围选择" expanded="true"  managed="false">
      <GridPane hgap="10" vgap="10">
         <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="100" prefWidth="100" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="200" prefWidth="200" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="100" prefWidth="100" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="200" prefWidth="200" />
         </columnConstraints>
         <rowConstraints>
            <RowConstraints minHeight="30" prefHeight="30" vgrow="SOMETIMES" />
         </rowConstraints>
         
         <Label text="开始时间:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
         <DatePicker fx:id="startDatePicker" GridPane.columnIndex="1" GridPane.rowIndex="0" />
         <Label text="结束时间:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
         <DatePicker fx:id="endDatePicker" GridPane.columnIndex="3" GridPane.rowIndex="0" />
      </GridPane>
   </TitledPane>
   
   <!-- 第三部分：导出设置 -->
   <TitledPane text="导出设置" expanded="true">
      <VBox spacing="10">
         <HBox spacing="10" alignment="CENTER_LEFT">
            <Label text="导出文件夹:" />
            <TextField fx:id="exportPathField" promptText="请选择导出文件夹" 
                      HBox.hgrow="ALWAYS" editable="false" />
            <Button fx:id="selectExportButton" text="选择文件夹" onAction="#selectExportFolder" />
         </HBox>
         <Button fx:id="startProcessButton" text="开始处理" onAction="#startProcessing" 
                 style="-fx-font-size: 14px; -fx-padding: 10px;" />
      </VBox>
   </TitledPane>
   
   <!-- 第四部分：处理进度 -->
   <TitledPane text="处理进度" expanded="true">
      <VBox spacing="10">
         <ProgressBar fx:id="progressBar" prefWidth="Infinity" prefHeight="25" />
         <Label fx:id="statusLabel" text="准备就绪，请选择源文件夹" 
                style="-fx-font-weight: bold;" />
         <TextArea fx:id="logArea" promptText="处理日志将在这里显示..." 
                   prefRowCount="8" editable="false" 
                   style="-fx-font-family: 'Consolas', 'Monaco', monospace; -fx-font-size: 11px;" />
      </VBox>
   </TitledPane>
   
</VBox> 