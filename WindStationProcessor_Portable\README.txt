# Wind Station Processor 便携式版本

## 🎯 特点
- ✅ 包含完整Python环境
- ✅ 体积相对较小（约100-200MB）
- ✅ 启动速度快
- ✅ 无需系统安装Python

## 📦 安装步骤

### 1. 下载便携式Python
1. 访问: https://www.python.org/downloads/windows/
2. 下载: Windows embeddable package (64-bit)
3. 解压到 `python/` 目录

### 2. 安装依赖
双击运行: `install_dependencies.bat`

### 3. 启动程序
双击运行: `启动程序.bat`

## 📁 目录结构
```
WindStationProcessor_Portable/
├── python/              # 便携式Python运行时
├── app/                 # 应用程序文件
│   ├── wind_station_minimal.py
│   ├── wind_station_processor_ultra_optimized.py
│   └── requirements.txt
├── 启动程序.bat         # 启动脚本
├── install_dependencies.bat  # 安装脚本
└── README.txt           # 本说明文件
```

## 🚀 使用方法
1. 按提示输入数据文件路径
2. 按提示输入状态文件路径
3. 等待处理完成

## 💡 优势
- 体积比exe文件小50-70%
- 启动速度比大型exe快
- 包含完整Python环境
- 易于维护和更新

## 📞 技术支持
如有问题请联系技术支持团队。
